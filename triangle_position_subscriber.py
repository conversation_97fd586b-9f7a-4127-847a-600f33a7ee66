#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, DurabilityPolicy
from px4_msgs.msg import SensorGps
import math

class TrianglePositionSubscriber(Node):
    
    def __init__(self):
        super().__init__('triangle_position_subscriber')
        
        # Dictionary to store latest GPS positions for each drone
        self.drone_positions = {
            1: {'lat': 0.0, 'lon': 0.0, 'alt': 0.0, 'timestamp': 0, 'fix_type': 0},
            2: {'lat': 0.0, 'lon': 0.0, 'alt': 0.0, 'timestamp': 0, 'fix_type': 0},
            3: {'lat': 0.0, 'lon': 0.0, 'alt': 0.0, 'timestamp': 0, 'fix_type': 0}
        }
        
        # Configure QoS profile for PX4 compatibility
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            durability=DurabilityPolicy.TRANSIENT_LOCAL,
            history=HistoryPolicy.KEEP_LAST,
            depth=1
        )
        
        # Create subscribers for each drone's GPS position
        self.drone1_sub = self.create_subscription(
            SensorGps,
            '/px4_1/fmu/out/vehicle_gps_position',
            lambda msg: self.gps_callback(msg, 1),
            qos_profile
        )
        
        self.drone2_sub = self.create_subscription(
            SensorGps,
            '/px4_2/fmu/out/vehicle_gps_position',
            lambda msg: self.gps_callback(msg, 2),
            qos_profile
        )
        
        self.drone3_sub = self.create_subscription(
            SensorGps,
            '/px4_3/fmu/out/vehicle_gps_position',
            lambda msg: self.gps_callback(msg, 3),
            qos_profile
        )
        
        # Timer to periodically print triangle formation status
        self.timer = self.create_timer(1.0, self.print_triangle_status)
        
        self.get_logger().info('Triangle GPS Subscriber initialized')
        self.get_logger().info('Subscribing to:')
        self.get_logger().info('  /px4_1/fmu/out/vehicle_gps_position')
        self.get_logger().info('  /px4_2/fmu/out/vehicle_gps_position')
        self.get_logger().info('  /px4_3/fmu/out/vehicle_gps_position')
    
    def gps_callback(self, msg, drone_id):
        """Callback function to handle GPS updates from drones"""
        self.drone_positions[drone_id] = {
            'lat': msg.latitude_deg,
            'lon': msg.longitude_deg,
            'alt': msg.altitude_msl_m,
            'timestamp': msg.timestamp,
            'fix_type': msg.fix_type
        }
        
        fix_type_names = {1: 'NONE', 2: '2D', 3: '3D', 4: 'RTCM', 5: 'RTK_FLOAT', 6: 'RTK_FIXED', 8: 'EXTRAPOLATED'}
        fix_name = fix_type_names.get(msg.fix_type, 'UNKNOWN')
        self.get_logger().debug(f'Drone {drone_id} GPS: lat={msg.latitude_deg:.7f}, lon={msg.longitude_deg:.7f}, alt={msg.altitude_msl_m:.2f}m, fix={fix_name}')
    
    def calculate_distance(self, pos1, pos2):
        """Calculate distance between two GPS positions using Haversine formula"""
        # Earth radius in meters
        R = 6371000.0
        
        # Convert degrees to radians
        lat1 = math.radians(pos1['lat'])
        lon1 = math.radians(pos1['lon'])
        lat2 = math.radians(pos2['lat'])
        lon2 = math.radians(pos2['lon'])
        
        # Haversine formula for horizontal distance
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        horizontal_distance = R * c
        
        # Vertical distance
        vertical_distance = abs(pos2['alt'] - pos1['alt'])
        
        # 3D distance
        return math.sqrt(horizontal_distance**2 + vertical_distance**2)
    
    def calculate_triangle_area(self):
        """Calculate the area of triangle formed by three drones using Heron's formula"""
        pos1 = self.drone_positions[1]
        pos2 = self.drone_positions[2]
        pos3 = self.drone_positions[3]
        
        # Calculate side lengths
        a = self.calculate_distance(pos1, pos2)  # Distance between drone 1 and 2
        b = self.calculate_distance(pos2, pos3)  # Distance between drone 2 and 3
        c = self.calculate_distance(pos3, pos1)  # Distance between drone 3 and 1
        
        # Calculate semi-perimeter
        s = (a + b + c) / 2.0
        
        # Calculate area using Heron's formula
        if s > a and s > b and s > c:
            area = math.sqrt(s * (s - a) * (s - b) * (s - c))
        else:
            area = 0.0
            
        return area, a, b, c
    
    def print_triangle_status(self):
        """Print current triangle formation status"""
        # Check if we have recent data from all drones
        current_time = self.get_clock().now().nanoseconds
        active_drones = []
        
        for drone_id, pos in self.drone_positions.items():
            if pos['timestamp'] > 0 and pos['fix_type'] >= 2:  # Has GPS fix (2D or better)
                active_drones.append(drone_id)
        
        if len(active_drones) < 3:
            self.get_logger().info(f'Waiting for position data... Active drones: {active_drones}')
            return
        
        # Calculate triangle properties
        area, side_a, side_b, side_c = self.calculate_triangle_area()
        
        # Print GPS positions
        self.get_logger().info('=== Triangle Formation Status ===')
        for drone_id in [1, 2, 3]:
            pos = self.drone_positions[drone_id]
            fix_type_names = {1: 'NONE', 2: '2D', 3: '3D', 4: 'RTCM', 5: 'RTK_FLOAT', 6: 'RTK_FIXED', 8: 'EXTRAPOLATED'}
            fix_name = fix_type_names.get(pos['fix_type'], 'UNKNOWN')
            self.get_logger().info(f'Drone {drone_id}: lat={pos["lat"]:11.7f}°, lon={pos["lon"]:11.7f}°, alt={pos["alt"]:6.2f}m, fix={fix_name}')
        
        # Print triangle metrics
        self.get_logger().info('=== Triangle Metrics ===')
        self.get_logger().info(f'Side lengths: {side_a:.2f}m, {side_b:.2f}m, {side_c:.2f}m')
        self.get_logger().info(f'Triangle area: {area:.2f} m²')
        
        # Calculate centroid
        centroid_lat = (self.drone_positions[1]['lat'] + self.drone_positions[2]['lat'] + self.drone_positions[3]['lat']) / 3.0
        centroid_lon = (self.drone_positions[1]['lon'] + self.drone_positions[2]['lon'] + self.drone_positions[3]['lon']) / 3.0
        centroid_alt = (self.drone_positions[1]['alt'] + self.drone_positions[2]['alt'] + self.drone_positions[3]['alt']) / 3.0
        
        self.get_logger().info(f'Formation centroid: lat={centroid_lat:.7f}°, lon={centroid_lon:.7f}°, alt={centroid_alt:.2f}m')
        self.get_logger().info('=' * 35)

def main(args=None):
    rclpy.init(args=args)
    
    triangle_subscriber = TrianglePositionSubscriber()
    
    try:
        rclpy.spin(triangle_subscriber)
    except KeyboardInterrupt:
        triangle_subscriber.get_logger().info('Shutting down Triangle Position Subscriber')
    finally:
        triangle_subscriber.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()