#!/bin/bash

# PX4 Three Drone Triangle Formation Simulation Script

SESSION_NAME="px4_triangle"

# Check if tmux is installed
if ! command -v tmux &> /dev/null; then
    echo "Error: tmux not found, please install tmux"
    echo "Ubuntu/Debian: sudo apt install tmux"
    echo "macOS: brew install tmux"
    exit 1
fi

# Detect PX4-Autopilot directory
PX4_PATH=""
if [ -f "./build/px4_sitl_default/bin/px4" ]; then
    PX4_PATH="."
    echo "Detected current directory as PX4-Autopilot directory"
elif [ -f "./PX4-Autopilot/build/px4_sitl_default/bin/px4" ]; then
    PX4_PATH="./PX4-Autopilot"
    echo "Detected PX4-Autopilot directory: $PX4_PATH"
elif [ -f "../build/px4_sitl_default/bin/px4" ]; then
    PX4_PATH=".."
    echo "Detected in PX4-Autopilot subdirectory, using parent directory"
else
    echo "Error: PX4-Autopilot directory or PX4 binary not found"
    echo "Please ensure:"
    echo "1. Run script within PX4-Autopilot directory, or"
    echo "2. Run in directory containing PX4-Autopilot subdirectory, or"
    echo "3. Compile PX4 first: make px4_sitl_default"
    exit 1
fi

# Check MicroXRCEAgent availability
if ! command -v MicroXRCEAgent &> /dev/null; then
    echo "Warning: MicroXRCEAgent not found, please ensure Micro-XRCE-DDS-Agent is installed"
fi

# Cleanup function - stop tmux session
cleanup() {
    echo ""
    echo "Stopping PX4 triangle simulation..."
    if tmux has-session -t $SESSION_NAME 2>/dev/null; then
        # Send Ctrl+C to all windows
        tmux list-windows -t $SESSION_NAME -F "#{window_index}" | while read window; do
            tmux send-keys -t $SESSION_NAME:$window C-c 2>/dev/null
        done

        # Wait for processes to end
        sleep 2

        # Force terminate session
        tmux kill-session -t $SESSION_NAME 2>/dev/null
        echo "PX4 triangle simulation stopped"
    fi
    exit 0
}

# Set signal handling
trap cleanup SIGINT SIGTERM

# If session exists, ask user what to do
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "Session '$SESSION_NAME' already exists"
    echo "Choose action:"
    echo "1. Connect to existing session"
    echo "2. Terminate existing session and create new one"
    echo "3. Exit"
    read -p "Please choose (1-3): " choice

    case $choice in
        1)
            echo "Connecting to existing session..."
            echo "Use Ctrl+C to stop entire simulation"
            tmux attach-session -t $SESSION_NAME
            exit 0
            ;;
        2)
            tmux kill-session -t $SESSION_NAME
            echo "Terminated existing session"
            ;;
        3)
            exit 0
            ;;
        *)
            echo "Invalid choice, exiting"
            exit 1
            ;;
    esac
fi

echo "Creating tmux session: $SESSION_NAME"
echo "PX4 path: $PX4_PATH"

# Get absolute path
PX4_ABS_PATH=$(cd "$PX4_PATH" && pwd)

# Triangle formation positions (equilateral triangle, 5m side length)
# Drone 1: Center (0, 0, 0)
# Drone 2: Forward-right (2.5, 4.33, 0) 
# Drone 3: Forward-left (-2.5, 4.33, 0)

# Create session and start first window - PX4 instance 1 (center)
tmux new-session -d -s $SESSION_NAME -n "Drone1-Center" \
"echo 'Starting PX4 instance 1 (Center drone - x500)...';
echo 'Working directory: $PX4_ABS_PATH';
cd '$PX4_ABS_PATH' && \
PX4_SYS_AUTOSTART=4001 PX4_SIM_MODEL=gz_x500 PX4_GZ_SIM_SYS_ID=1 \
./build/px4_sitl_default/bin/px4 -i 1;
echo ''; echo 'PX4 instance 1 exited - press any key to close window'; read -n 1"

# Create second window - PX4 instance 2 (forward-right)
tmux new-window -t $SESSION_NAME -n "Drone2-Right" \
"echo 'Starting PX4 instance 2 (Right drone - x500)...';
echo 'Working directory: $PX4_ABS_PATH';
cd '$PX4_ABS_PATH' && \
PX4_GZ_STANDALONE=1 PX4_SYS_AUTOSTART=4001 PX4_GZ_MODEL_POSE='-2.5,2.5,0' \
PX4_SIM_MODEL=gz_x500 PX4_GZ_SIM_SYS_ID=2 \
./build/px4_sitl_default/bin/px4 -i 2;
echo ''; echo 'PX4 instance 2 exited - press any key to close window'; read -n 1"

# Create third window - PX4 instance 3 (forward-left)
tmux new-window -t $SESSION_NAME -n "Drone3-Left" \
"echo 'Starting PX4 instance 3 (Left drone - x500)...';
echo 'Working directory: $PX4_ABS_PATH';
cd '$PX4_ABS_PATH' && \
PX4_GZ_STANDALONE=1 PX4_SYS_AUTOSTART=4001 PX4_GZ_MODEL_POSE='2.5,2.5,0' \
PX4_SIM_MODEL=gz_x500 PX4_GZ_SIM_SYS_ID=3 \
./build/px4_sitl_default/bin/px4 -i 3;
echo ''; echo 'PX4 instance 3 exited - press any key to close window'; read -n 1"

# Create fourth window - MicroXRCE agent
tmux new-window -t $SESSION_NAME -n "XRCE-Agent" \
"echo 'Starting MicroXRCE agent (UDP port 8888)...';
if command -v MicroXRCEAgent &> /dev/null; then
    MicroXRCEAgent udp4 -p 8888;
else
    echo 'Error: MicroXRCEAgent not found';
    echo 'Please install: sudo apt install micro-xrce-dds-agent';
    echo 'Or compile install: https://github.com/eProsima/Micro-XRCE-DDS-Agent';
fi;
echo ''; echo 'MicroXRCE agent exited - press any key to close window'; read -n 1"

# Create monitor window
tmux new-window -t $SESSION_NAME -n "Monitor" \
"echo '=== PX4 Triangle Formation Simulation Monitor ===';
echo '';
echo 'Window navigation:';
echo '  0: Drone1-Center - PX4 instance 1 (Center, x500)';
echo '  1: Drone2-Right  - PX4 instance 2 (Right, x500)';
echo '  2: Drone3-Left   - PX4 instance 3 (Left, x500)';
echo '  3: XRCE-Agent    - MicroXRCE agent';
echo '  4: Monitor       - Current monitor window';
echo '';
echo 'Triangle formation:';
echo '  Drone 1: Center (0, 0, 0)';
echo '  Drone 2: Right (2.5, 4.33, 0)';
echo '  Drone 3: Left (-2.5, 4.33, 0)';
echo '';
echo 'tmux shortcuts:';
echo '  Ctrl+b + 0/1/2/3/4  → Switch to corresponding window';
echo '  Ctrl+b + n        → Next window';
echo '  Ctrl+b + p        → Previous window';
echo '  Ctrl+b + w        → Window list';
echo '  Ctrl+b + d        → Detach session (run in background)';
echo '';
echo 'Exit simulation:';
echo '  Press Ctrl+C         → Stop all processes and exit';
echo '  Or run: tmux kill-session -t $SESSION_NAME';
echo '';
echo 'Reconnect to detached session:';
echo '  tmux attach-session -t $SESSION_NAME';
echo '';
echo 'Press q to exit monitor window';
echo '';
while true; do
    echo -n 'Monitoring... ';
    date '+%H:%M:%S';
    echo 'Session status: Running';
    if tmux list-windows -t $SESSION_NAME 2>/dev/null | grep -q 'Drone1-Center\|Drone2-Right\|Drone3-Left\|XRCE-Agent'; then
        echo 'Process status: Normal';
    else
        echo 'Process status: Some processes may have exited';
    fi;
    echo 'Press Ctrl+C to stop simulation, press other key to refresh status...';
    if read -t 5 -n 1 key; then
        if [ \"\$key\" = \"q\" ]; then
            break;
        fi;
    fi;
    clear;
    echo '=== PX4 Triangle Formation Simulation Monitor ===';
    echo 'Windows: 0:Center 1:Right 2:Left 3:XRCE-Agent 4:Monitor';
    echo '';
done"

# Select first window
tmux select-window -t $SESSION_NAME:0

echo ""
echo "✅ tmux session created!"
echo ""
echo "🚁 Triangle formation layout:"
echo "  0: Drone1-Center - PX4 instance 1 (Center position, x500)"
echo "  1: Drone2-Right  - PX4 instance 2 (Right position, x500)"
echo "  2: Drone3-Left   - PX4 instance 3 (Left position, x500)"
echo "  3: XRCE-Agent    - MicroXRCE agent (UDP:8888)"
echo "  4: Monitor       - Monitor window"
echo ""
echo "📐 Triangle positions:"
echo "  Drone 1: Center (0, 0, 0)"
echo "  Drone 2: Right (2.5, 4.33, 0)"
echo "  Drone 3: Left (-2.5, 4.33, 0)"
echo ""
echo "🎮 Control instructions:"
echo "  Ctrl+b + number key   → Quick switch window"
echo "  Ctrl+b + n/p          → Next/previous window"
echo "  Ctrl+b + w            → Window selection list"
echo "  Ctrl+b + d            → Detach session (run in background)"
echo ""
echo "🛑 Exit simulation:"
echo "  Press Ctrl+C           → Stop all processes and exit"
echo ""
echo "🔄 Reconnect command:"
echo "  tmux attach-session -t $SESSION_NAME"
echo ""
echo "Connecting to session..."
sleep 2

# Connect to session with cleanup on exit
tmux attach-session -t $SESSION_NAME

# Clean up when user exits tmux
cleanup
